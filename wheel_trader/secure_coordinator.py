"""
Secure Coordinator for smolagents + E2B Integration

This module provides a secure coordinator that manages smolagents execution
through E2B sandboxes with health monitoring and performance tracking.
"""

import time
from typing import Any, Dict, List, Optional

from .agent_health import AgentHealthManager
from .evaluator import evaluate_run
from .smolagents_e2b import (
    HealthAwareAgent,
    market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
)


class SecureCoordinator:
    """
    Enhanced coordinator with native E2B security and health monitoring.
    Manages secure execution of smolagents through native E2B support.
    """

    def __init__(self, health_threshold: float = 0.7):
        self.health_manager = AgentHealthManager(health_threshold)
        self.agents = {}
        self.available_tools = {
            "options_data": options_data_tool,
            "market_analysis": market_analysis_tool,
            "risk_assessment": risk_assessment_tool
        }

    def register_agent(self, name: str, agent_instance: HealthAwareAgent):
        """Register an agent with the coordinator"""
        self.agents[name] = agent_instance

    def create_secure_agent(self, name: str, model=None, tools: Optional[List] = None) -> HealthAwareAgent:
        """
        Create a new secure agent with native E2B execution.

        Args:
            name: Agent name
            model: LLM model to use (defaults to InferenceClientModel)
            tools: Additional tools to include

        Returns:
            HealthAwareAgent instance with E2B execution
        """
        if tools is None:
            tools = list(self.available_tools.values())
        else:
            tools.extend(self.available_tools.values())

        agent = HealthAwareAgent(name=name, model=model, tools=tools)
        self.register_agent(name, agent)
        return agent

    def execute_agent(self, agent_name: str, task: str, **kwargs) -> Dict[str, Any]:
        """Execute a specific agent with health checking and secure execution"""
        if agent_name not in self.agents:
            return {
                "success": False,
                "error": f"Agent '{agent_name}' not found",
                "result": None,
                "health_check": "skipped"
            }

        # Check agent health before execution
        if not self.health_manager.is_agent_healthy(agent_name):
            health_metrics = self.health_manager.get_health_metrics(agent_name)
            health_score = health_metrics.health_score if health_metrics else 0.0

            return {
                "success": False,
                "error": f"Agent '{agent_name}' is unhealthy (score: {health_score:.2f})",
                "result": None,
                "health_check": "failed",
                "health_score": health_score
            }

        start_time = time.time()

        try:
            agent = self.agents[agent_name]

            # Execute the agent (will use E2B for code execution)
            if hasattr(agent, 'run'):
                result = agent.run(task, **kwargs)
            elif hasattr(agent, 'execute'):
                result = agent.execute(task, **kwargs)
            else:
                # Try calling the agent directly
                result = agent(task, **kwargs)

            execution_time = time.time() - start_time

            # Evaluate the result
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 0,
                    "duration_ms": int(execution_time * 1000),
                    "output": str(result)
                }
            )

            # Update agent health after successful execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": True,
                "result": result,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed"
            }

        except Exception as e:
            execution_time = time.time() - start_time

            # Log the error
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 1,
                    "duration_ms": int(execution_time * 1000),
                    "error": str(e)
                }
            )

            # Update agent health after failed execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed"
            }

    def execute_tool_directly(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a tool directly without going through an agent.

        Args:
            tool_name: Name of the tool to execute
            **kwargs: Arguments to pass to the tool

        Returns:
            Execution result dictionary
        """
        if tool_name not in self.available_tools:
            return {
                "success": False,
                "error": f"Tool '{tool_name}' not found",
                "result": None
            }

        start_time = time.time()

        try:
            tool = self.available_tools[tool_name]
            result = tool(**kwargs)  # Direct tool call

            execution_time = time.time() - start_time

            return {
                "success": True,
                "result": result,
                "execution_time": execution_time,
                "tool": tool_name
            }

        except Exception as e:
            execution_time = time.time() - start_time

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "execution_time": execution_time,
                "tool": tool_name
            }

    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all registered agents including health metrics"""
        health_data = self.health_manager.get_all_agent_health()
        unhealthy_agents = self.health_manager.get_unhealthy_agents()

        return {
            "registered_agents": list(self.agents.keys()),
            "total_agents": len(self.agents),
            "available_tools": list(self.available_tools.keys()),
            "health_threshold": self.health_manager.health_threshold,
            "unhealthy_agents": unhealthy_agents,
            "health_summary": {
                name: {
                    "score": metrics.health_score,
                    "total_runs": metrics.total_runs,
                    "success_rate": metrics.success_rate
                }
                for name, metrics in health_data.items()
            }
        }

    def refresh_agent_health(self) -> Dict[str, Any]:
        """Refresh health scores for all agents"""
        updated_count = self.health_manager.refresh_all_health_scores()

        return {
            "updated_agents": updated_count,
            "timestamp": time.time()
        }
