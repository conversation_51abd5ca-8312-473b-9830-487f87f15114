from flask import Blueprint, request, jsonify
from supabase import create_client, Client
import wheel_trader.config as config
from wheel_trader.exec_manager_e2b import run_in_sandbox
from wheel_trader.evaluator import evaluate_run
from wheel_trader.observability import trace_and_remember

# Create blueprint
coordinator_app = Blueprint('coordinator', __name__)

# In-memory cache for agent health scores
agent_health_cache = {}

def get_agent_health(agent_name, agent_version):
    """
    Retrieves agent health score, with a simple in-memory cache.
    """
    supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
    if (agent_name, agent_version) in agent_health_cache:
        return agent_health_cache[(agent_name, agent_version)]

    response = supabase.table("agent_health").select("score").eq("agent_name", agent_name).eq("agent_version", agent_version).execute()
    if response.data:
        score = response.data[0]['score']
        agent_health_cache[(agent_name, agent_version)] = score
        return score
    return 1.0 # Default to healthy if no score is found

@coordinator_app.route('/execute_task', methods=['POST'])
@trace_and_remember
def execute_task():
    data = request.json
    task = data.get('task')
    agent_name = "nlp_agent" # Placeholder for agent selection logic
    agent_version = "1.0" # Placeholder for versioning

    # Health Gate
    health_score = get_agent_health(agent_name, agent_version)
    if health_score < 0.7:
        return jsonify({"error": f"Agent {agent_name} v{agent_version} is unhealthy (score: {health_score}) and cannot be executed."}), 503

    # For simplicity, we'll pass the task to a single agent.
    # In a real system, this would involve more complex routing.
    # The code to be executed in the sandbox:
    agent_code = f"""
from agent_nlp.nlp_agent import process_message
result = process_message('{task}')
print(result)
"""

    # Execute in sandbox
    execution_result = run_in_sandbox(agent_name, agent_code)

    # Evaluate the run
    evaluate_run(agent_name, agent_version, execution_result)

    return jsonify({
        'task': task,
        'result': execution_result.get('stdout'),
        'exit_code': execution_result.get('exit_code'),
        'stderr': execution_result.get('stderr')
    })
