import phoenix as px
from functools import wraps
from wheel_trader.memory_writer import save_memory as save_memory_func

def trace_and_remember(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        with px.trace(f"{func.__module__}.{func.__name__}") as span:
            result = func(*args, **kwargs)
            save_memory_func(span.context.trace_id, result)
            return result
    return wrapper
