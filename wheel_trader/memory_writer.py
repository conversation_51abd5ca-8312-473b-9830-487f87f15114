import openai
import wheel_trader.config as config
from supabase import create_client, Client

def get_embedding(text, model="text-embedding-3-small"):
    openai.api_key = config.OPENAI_API_KEY
    text = text.replace("\n", " ")
    return openai.Embedding.create(input = [text], model=model)['data'][0]['embedding']

def save_memory(trace_id, payload):
    supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
    content = str(payload)
    embedding = get_embedding(content)
    data = {
        "trace_id": str(trace_id),
        "mem_type": "agent_action",
        "content": content,
        "embedding": embedding,
        "json_blob": payload
    }
    supabase.table("memory_embeddings").insert(data).execute()
