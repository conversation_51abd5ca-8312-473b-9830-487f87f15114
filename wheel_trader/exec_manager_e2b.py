import os
import time
import uuid
from datetime import datetime

from dotenv import load_dotenv
from e2b_code_interpreter import Sandbox
from supabase import Client, create_client

import wheel_trader.config as config

load_dotenv()

# Allowed domains for network ACL
ALLOWED_DOMAINS = [
    "polygon.io",
    "supabase.co",
    "api.openai.com",
    "barchart.com",
    "marketdata.app",
    "optionwatch.io"
]


def log_execution_audit(container_id, agent_name, job_name, cpu_ms, mem_peak_mb,
                       duration_ms, exit_code, success, error_message=None):
    """Log execution details to audit table"""
    try:
        if not config.SUPABASE_URL or not config.SUPABASE_KEY:
            print("Supabase credentials not configured, skipping audit log")
            return

        supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        audit_data = {
            "container_id": container_id,
            "agent_name": agent_name,
            "job_name": job_name,
            "cpu_ms": cpu_ms,
            "mem_peak_mb": mem_peak_mb,
            "duration_ms": duration_ms,
            "exit_code": exit_code,
            "success": success,
            "error_message": error_message,
            "created_at": datetime.now().isoformat()
        }
        supabase.table("exec_audit_log").insert(audit_data).execute()
    except Exception as e:
        print(f"Failed to log execution audit: {e}")

def run_job(name, code_path, env=None):
    """
    Runs the given Python file in an e2b sandbox with resource/network limits.
    Returns a dict with execution stats and output.
    """
    container_id = str(uuid.uuid4())
    start_time = time.time()
    agent_name = name
    job_name = os.path.basename(code_path) if code_path else "unknown"

    # Read the code to execute
    try:
        with open(code_path, "r") as f:
            code = f.read()
    except FileNotFoundError:
        error_msg = f"Code file not found: {code_path}"
        log_execution_audit(container_id, agent_name, job_name, 0, 0, 0, -1, False, error_msg)
        return {
            "job_name": job_name,
            "container_id": container_id,
            "success": False,
            "exit_code": -1,
            "cpu_ms": 0,
            "duration_ms": 0,
            "output": "",
            "error": error_msg
        }

    # Prepare environment variables
    exec_env = os.environ.copy()
    if env:
        exec_env.update(env)

    try:
        # Create sandbox with resource limits
        # Note: e2b-code-interpreter doesn't expose direct resource limits in Python SDK
        # These would be configured at the e2b account/template level
        sbx = Sandbox()

        # Execute code with timeout
        execution = sbx.run_code(code, timeout=30.0)  # 30 second timeout

        # Extract execution results
        output = ""
        if execution.logs and execution.logs.stdout:
            output = "\n".join(execution.logs.stdout)

        error = ""
        if execution.error:
            error = f"{execution.error.name}: {execution.error.value}"
        elif execution.logs and execution.logs.stderr:
            error = "\n".join(execution.logs.stderr)

        exit_code = 0 if not execution.error else 1
        success = not bool(execution.error)

        # Note: e2b-code-interpreter Sandbox doesn't have close() method
        # Sandbox is automatically cleaned up

    except Exception as e:
        output = ""
        error = str(e)
        exit_code = -1
        success = False

    end_time = time.time()
    duration_ms = int((end_time - start_time) * 1000)

    # Estimate resource usage (e2b doesn't provide direct metrics)
    cpu_ms = duration_ms  # Rough estimate
    mem_peak_mb = 50  # Default estimate for small jobs

    # Log execution audit
    log_execution_audit(
        container_id=container_id,
        agent_name=agent_name,
        job_name=job_name,
        cpu_ms=cpu_ms,
        mem_peak_mb=mem_peak_mb,
        duration_ms=duration_ms,
        exit_code=exit_code,
        success=success,
        error_message=error if error else None
    )

    return {
        "job_name": job_name,
        "container_id": container_id,
        "success": success,
        "exit_code": exit_code,
        "cpu_ms": cpu_ms,
        "duration_ms": duration_ms,
        "output": output,
        "error": error
    }


def run_code_string(name, code, env=None):
    """
    Runs the given Python code string in an e2b sandbox.
    Returns a dict with execution stats and output.
    """
    container_id = str(uuid.uuid4())
    start_time = time.time()
    agent_name = name
    job_name = "code_string"

    # Prepare environment variables
    exec_env = os.environ.copy()
    if env:
        exec_env.update(env)

    try:
        # Create sandbox
        sbx = Sandbox()

        # Execute code with timeout
        execution = sbx.run_code(code, timeout=30.0)

        # Extract execution results
        output = ""
        if execution.logs and execution.logs.stdout:
            output = "\n".join(execution.logs.stdout)

        error = ""
        if execution.error:
            error = f"{execution.error.name}: {execution.error.value}"
        elif execution.logs and execution.logs.stderr:
            error = "\n".join(execution.logs.stderr)

        exit_code = 0 if not execution.error else 1
        success = not bool(execution.error)

    except Exception as e:
        output = ""
        error = str(e)
        exit_code = -1
        success = False

    end_time = time.time()
    duration_ms = int((end_time - start_time) * 1000)

    # Estimate resource usage
    cpu_ms = duration_ms
    mem_peak_mb = 50

    # Log execution audit
    log_execution_audit(
        container_id=container_id,
        agent_name=agent_name,
        job_name=job_name,
        cpu_ms=cpu_ms,
        mem_peak_mb=mem_peak_mb,
        duration_ms=duration_ms,
        exit_code=exit_code,
        success=success,
        error_message=error if error else None
    )

    return {
        "job_name": job_name,
        "container_id": container_id,
        "success": success,
        "exit_code": exit_code,
        "cpu_ms": cpu_ms,
        "duration_ms": duration_ms,
        "output": output,
        "error": error
    }
