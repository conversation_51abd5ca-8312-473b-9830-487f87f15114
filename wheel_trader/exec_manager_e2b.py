import os
import uuid
import time
from dotenv import load_dotenv
from e2b_code_interpreter import Sandbox, SandboxConfig

load_dotenv()

def run_job(name, code_path, env=None):
    """
    Runs the given Python file in an e2b sandbox with resource/network limits.
    Returns a dict with execution stats and output.
    """
    container_id = str(uuid.uuid4())
    start_time = time.time()

    # Read the code to execute
    with open(code_path, "r") as f:
        code = f.read()

    # Prepare environment variables
    exec_env = os.environ.copy()
    if env:
        exec_env.update(env)

    try:
        sbx = Sandbox(env=exec_env)
        execution = sbx.run_code(code)
        output = execution.logs
        error = ""  # e2b_code_interpreter does not provide a separate error field in this minimal example
        exit_code = 0  # Not available in minimal example
        success = True
    except Exception as e:
        output = ""
        error = str(e)
        exit_code = -1
        success = False

    end_time = time.time()
    duration_ms = int((end_time - start_time) * 1000)

    # Simulate resource usage (to be replaced with real stats from e2b)
    cpu_ms = duration_ms
    mem_peak = 0  # Not measured in this placeholder

    return {
        "container_id": container_id,
        "success": success,
        "output": output,
        "error": error,
        "exit_code": exit_code,
        "cpu_ms": cpu_ms,
        "mem_peak": mem_peak,
        "duration_ms": duration_ms,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "job_name": name,
        "code_path": code_path,
    }
