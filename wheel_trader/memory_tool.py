from supabase import create_client, Client
import wheel_trader.config as config
import openai

openai.api_key = config.OPENAI_API_KEY

def get_embedding(text, model="text-embedding-3-small"):
   text = text.replace("\n", " ")
   return openai.Embedding.create(input = [text], model=model)['data'][0]['embedding']

class MemoryTool:
    def run(self, query: str):
        supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        embedding = get_embedding(query)
        result = supabase.rpc(
            "match_memories",
            {
                "query_embedding": embedding,
                "match_threshold": 0.78,
                "match_count": 5,
            },
        ).execute()
        return result.data
