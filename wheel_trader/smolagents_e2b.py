"""
Native smolagents + E2B Integration

This module provides health-aware smolagents agents that use
smolagents' built-in E2B support with added health monitoring and audit logging.
"""

import time
from typing import Any, Dict

from agents.options_data import market_analysis_tool, options_data_tool, risk_assessment_tool
from smolagents import CodeAgent, InferenceClientModel
from wheel_trader.exec_manager_e2b import log_execution_audit

from .agent_health import AgentHealthManager


class HealthAwareAgent:
    """
    Wrapper for smolagents CodeAgent with health monitoring and E2B execution.
    Uses native smolagents E2B support with added health checking.
    """

    def __init__(self, name: str, model=None, tools=None, health_threshold: float = 0.7):
        self.name = name
        self.health_manager = AgentHealthManager(health_threshold)

        # Use default model if none provided
        if model is None:
            model = InferenceClientModel()

        # Create agent with native E2B support
        self.agent = CodeAgent(
            model=model,
            tools=tools or [],
            executor_type="e2b",
            add_base_tools=False  # Don't add base tools to avoid dependencies
        )

    def run(self, task: str, **kwargs) -> Any:
        """
        Run agent with health checking and audit logging.
        """
        # Check agent health before execution
        if not self.health_manager.is_agent_healthy(self.name):
            health_metrics = self.health_manager.get_health_metrics(self.name)
            error_msg = f"Agent '{self.name}' is unhealthy (score: {health_score:.2f})"
            self._log_blocked_execution(self.name, error_msg)
            health_score = health_metrics.health_score if health_metrics else 0.0
            raise RuntimeError(error_msg)

        start_time = time.time()

        try:
            # Use context manager for automatic E2B cleanup
            with self.agent:
                result = self.agent.run(task, **kwargs)

            # Update health after successful execution
            self.health_manager.update_agent_health(self.name)

            return result

        except Exception:
            # Update health after failed execution
            self.health_manager.update_agent_health(self.name)
            raise

    def _log_blocked_execution(self, agent_name: str, error_msg: str):
        """Log when execution is blocked due to health issues"""
        log_execution_audit(
            container_id="blocked",
            agent_name=agent_name,
            job_name=self.name,
            cpu_ms=0,
            mem_peak_mb=0,
            duration_ms=0,
            exit_code=-2,
            success=False,
            error_message=error_msg
        )

    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of the agent"""
        metrics = self.health_manager.get_health_metrics(self.name)
        if metrics:
            return {
                "agent_name": self.name,
                "health_score": metrics.health_score,
                "total_runs": metrics.total_runs,
                "success_rate": metrics.success_rate,
                "avg_duration_ms": metrics.avg_duration_ms,
                "is_healthy": metrics.health_score >= self.health_manager.health_threshold
            }
        return {
            "agent_name": self.name,
            "health_score": 1.0,
            "is_healthy": True,
            "status": "new_agent"
        }

    def get_all_agent_health(self) -> Dict[str, Any]:
        """Get health status of all agents"""
        return self.health_manager.get_all_agent_health()


# These will automatically execute in E2B when used by CodeAgent

def create_trading_agent(name: str = "trading_agent", model=None) -> HealthAwareAgent:
    """
    Create a health-aware trading agent with E2B execution and domain-specific tools.

    Args:
        name: Name for the agent
        model: Model to use (defaults to InferenceClientModel)

    Returns:
        HealthAwareAgent configured for trading tasks
    """
    tools = [
        options_data_tool,
        market_analysis_tool,
        risk_assessment_tool
    ]

    return HealthAwareAgent(
        name=name,
        model=model,
        tools=tools
    )


def create_multi_agent_system(model=None) -> Dict[str, HealthAwareAgent]:
    """
    Create a multi-agent system with specialized agents.

    Returns:
        Dictionary of specialized agents
    """
    return {
        "data_agent": HealthAwareAgent(
            name="data_agent",
            model=model,
            tools=[options_data_tool]
        ),
        "analysis_agent": HealthAwareAgent(
            name="analysis_agent",
            model=model,
            tools=[market_analysis_tool]
        ),
        "risk_agent": HealthAwareAgent(
            name="risk_agent",
            model=model,
            tools=[risk_assessment_tool]
        ),
        "coordinator_agent": HealthAwareAgent(
            name="coordinator_agent",
            model=model,
            tools=[options_data_tool, market_analysis_tool, risk_assessment_tool]
        )
    }
