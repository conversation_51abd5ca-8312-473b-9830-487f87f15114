"""
smolagents + E2B Integration Adapter

This module provides secure execution of smolagents tools through E2B sandboxes.
All tool execution is routed through E2B for security and monitoring.
"""

import json
import time
from typing import Any, Dict

from smolagents import CodeAgent, PythonInterpreterTool, Tool

from .agent_health import <PERSON>HealthManager
from .exec_manager_e2b import log_execution_audit, run_code_string


class SecureE2BTool(Tool):
    """
    Base class for tools that execute securely in E2B sandbox.
    Extends smolagents Tool with E2B execution and health monitoring.
    """
    
    def __init__(self, name: str, description: str, inputs: Dict, output_type: str = "str"):
        super().__init__()
        self.name = name
        self.description = description
        self.inputs = inputs
        self.output_type = output_type
        self.health_manager = AgentHealthManager()
        
    def forward(self, *args, **kwargs) -> Any:
        """
        Execute tool in E2B sandbox with health checking and monitoring.
        """
        agent_name = f"{self.name}_tool"
        
        # Check agent health before execution
        if not self.health_manager.is_agent_healthy(agent_name):
            error_msg = f"Agent {agent_name} is unhealthy (score < 0.7), execution blocked"
            self._log_blocked_execution(agent_name, error_msg)
            raise RuntimeError(error_msg)
        
        # Generate Python code for execution
        code = self._generate_code(*args, **kwargs)
        
        # Execute in E2B sandbox
        start_time = time.time()
        try:
            result = run_code_string(agent_name, code)
            
            if result['success']:
                # Parse and return the output
                return self._parse_output(result['output'])
            else:
                raise RuntimeError(f"Tool execution failed: {result['error']}")
                
        except Exception as e:
            # Log the error and re-raise
            duration_ms = int((time.time() - start_time) * 1000)
            log_execution_audit(
                container_id="error",
                agent_name=agent_name,
                job_name=self.name,
                cpu_ms=0,
                mem_peak_mb=0,
                duration_ms=duration_ms,
                exit_code=-1,
                success=False,
                error_message=str(e)
            )
            raise
    
    def _generate_code(self, *args, **kwargs) -> str:
        """
        Generate Python code to execute in E2B sandbox.
        Override this method in subclasses.
        """
        raise NotImplementedError("Subclasses must implement _generate_code")
    
    def _parse_output(self, output: str) -> Any:
        """
        Parse the output from E2B execution.
        Override this method in subclasses if needed.
        """
        return output.strip()
    
    def _log_blocked_execution(self, agent_name: str, error_msg: str):
        """Log when execution is blocked due to health issues"""
        log_execution_audit(
            container_id="blocked",
            agent_name=agent_name,
            job_name=self.name,
            cpu_ms=0,
            mem_peak_mb=0,
            duration_ms=0,
            exit_code=-2,
            success=False,
            error_message=error_msg
        )


class SecureOptionsDataTool(SecureE2BTool):
    """Tool for fetching options data securely via E2B"""

    def __init__(self):
        super().__init__(
            name="options_data_fetcher",
            description="Fetch options data for a given symbol and expiration date",
            inputs={
                "symbol": {"type": "string", "description": "Stock symbol (e.g., AAPL)"},
                "expiry_date": {"type": "string", "description": "Expiration date (YYYY-MM-DD)"}
            },
            output_type="dict"
        )
    
    def _generate_code(self, symbol: str, expiry_date: str) -> str:
        return f"""
import os
import requests
import json
from datetime import datetime

# Get API key from environment
api_key = os.getenv('POLYGON_API_KEY')
if not api_key:
    print("ERROR: POLYGON_API_KEY not set")
    exit(1)

# Fetch options data
symbol = "{symbol}"
expiry_date = "{expiry_date}"

try:
    # Example: Fetch options chain from Polygon
    url = f"https://api.polygon.io/v3/reference/options/contracts"
    params = {{
        "underlying_ticker": symbol,
        "expiration_date": expiry_date,
        "apikey": api_key,
        "limit": 100
    }}
    
    response = requests.get(url, params=params, timeout=30)
    response.raise_for_status()
    
    data = response.json()
    
    # Return structured result
    result = {{
        "symbol": symbol,
        "expiry_date": expiry_date,
        "contracts_count": len(data.get("results", [])),
        "contracts": data.get("results", [])[:10],  # First 10 contracts
        "status": "success"
    }}
    
    print(json.dumps(result))
    
except Exception as e:
    error_result = {{
        "symbol": symbol,
        "expiry_date": expiry_date,
        "error": str(e),
        "status": "error"
    }}
    print(json.dumps(error_result))
"""
    
    def _parse_output(self, output: str) -> Dict:
        """Parse JSON output from options data fetch"""
        try:
            # Find the JSON in the output
            lines = output.strip().split('\n')
            for line in lines:
                if line.strip().startswith('{'):
                    return json.loads(line.strip())
            return {"error": "No JSON output found", "status": "error"}
        except json.JSONDecodeError as e:
            return {"error": f"JSON parse error: {e}", "status": "error"}


class SecureMarketAnalysisTool(SecureE2BTool):
    """Tool for performing market analysis securely via E2B"""

    def __init__(self):
        super().__init__(
            name="market_analysis",
            description="Perform technical analysis on market data",
            inputs={
                "symbol": {"type": "string", "description": "Stock symbol"},
                "analysis_type": {"type": "string", "description": "Type of analysis (rsi, sma, bollinger)"}
            },
            output_type="dict"
        )
    
    def _generate_code(self, symbol: str, analysis_type: str) -> str:
        return f"""
import json
import math
from datetime import datetime, timedelta

# Simulate market analysis (replace with real data fetching)
symbol = "{symbol}"
analysis_type = "{analysis_type}"

try:
    # Mock analysis results
    if analysis_type == "rsi":
        result = {{
            "symbol": symbol,
            "analysis_type": "rsi",
            "rsi_value": 65.5,
            "signal": "neutral",
            "timestamp": datetime.now().isoformat()
        }}
    elif analysis_type == "sma":
        result = {{
            "symbol": symbol,
            "analysis_type": "sma",
            "sma_20": 150.25,
            "sma_50": 148.75,
            "signal": "bullish",
            "timestamp": datetime.now().isoformat()
        }}
    else:
        result = {{
            "symbol": symbol,
            "analysis_type": analysis_type,
            "error": "Unsupported analysis type",
            "status": "error"
        }}
    
    print(json.dumps(result))
    
except Exception as e:
    error_result = {{
        "symbol": symbol,
        "analysis_type": analysis_type,
        "error": str(e),
        "status": "error"
    }}
    print(json.dumps(error_result))
"""
    
    def _parse_output(self, output: str) -> Dict:
        """Parse JSON output from market analysis"""
        try:
            lines = output.strip().split('\n')
            for line in lines:
                if line.strip().startswith('{'):
                    return json.loads(line.strip())
            return {"error": "No JSON output found", "status": "error"}
        except json.JSONDecodeError as e:
            return {"error": f"JSON parse error: {e}", "status": "error"}


class SecurePythonInterpreterTool(SecureE2BTool):
    """Secure Python interpreter that executes code in E2B sandbox"""

    def __init__(self):
        super().__init__(
            name="python_interpreter",
            description="Execute Python code securely in E2B sandbox",
            inputs={
                "code": {"type": "string", "description": "Python code to execute"}
            },
            output_type="str"
        )
    
    def _generate_code(self, code: str) -> str:
        # Return the code as-is for execution
        return code
    
    def _parse_output(self, output: str) -> str:
        # Return output as-is
        return output


class SecureCodeAgent(CodeAgent):
    """
    Secure CodeAgent that uses E2B for all code execution.
    Replaces the default PythonInterpreterTool with SecurePythonInterpreterTool.
    """
    
    def __init__(self, tools=None, model="gpt-4", **kwargs):
        # Initialize with secure tools
        if tools is None:
            tools = []
        
        # Add secure Python interpreter
        secure_python_tool = SecurePythonInterpreterTool()
        tools.append(secure_python_tool)
        
        # Add other secure tools
        tools.extend([
            SecureOptionsDataTool(),
            SecureMarketAnalysisTool()
        ])
        
        super().__init__(tools=tools, model=model, **kwargs)
        
        # Replace any existing PythonInterpreterTool with secure version
        self.tools = [tool for tool in self.tools if not isinstance(tool, PythonInterpreterTool)]
        self.tools.append(secure_python_tool)
