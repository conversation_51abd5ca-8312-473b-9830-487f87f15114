import requests
import os
from dotenv import load_dotenv

load_dotenv()


def fetch_options_data(symbol="AAPL"):
    """
    Fetches options data for a given symbol from polygon.io.
    Replace this with actual API logic as needed.
    """
    api_key = os.getenv("POLYGON_API_KEY")
    if not api_key:
        raise ValueError("POLYGON_API_KEY not set in environment variables.")
    url = (
        f"https://api.polygon.io/v3/reference/options/contracts"
        f"?underlying_ticker={symbol}&apiKey={api_key}"
    )
    response = requests.get(url)
    response.raise_for_status()
    return response.json()


if __name__ == "__main__":
    symbol = "AAPL"
    try:
        data = fetch_options_data(symbol)
        print(f"Options data for {symbol}:")
        print(data)
    except Exception as e:
        print(f"Error fetching options data: {e}")
