# exec_logger.py
import os
import time
import json
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
import psycopg2
from psycopg2.extras import RealDictCursor
from smolagents import OpenAIModel, CodeAgent

class ExecutionLogger:
    """Logs all agent executions to PostgreSQL database"""
    
    def __init__(self, db_url: str):
        self.db_url = db_url
        self.conn = None
        self._connect()
    
    def _connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(
                self.db_url,
                cursor_factory=RealDictCursor
            )
            self.conn.autocommit = True
            print("✅ Database connected successfully")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            raise
    
    def log_execution(self, 
                     agent_name: str,
                     task_prompt: str,
                     success: bool,
                     duration_ms: int,
                     error_message: Optional[str] = None,
                     resource_usage: Optional[Dict] = None,
                     sandbox_id: Optional[str] = None):
        """Insert execution record into database"""
        
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO exec_audit_log 
                    (agent_name, task_prompt, success, duration_ms, 
                     error_message, resource_usage, sandbox_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (
                    agent_name,
                    task_prompt,
                    success,
                    duration_ms,
                    error_message,
                    json.dumps(resource_usage) if resource_usage else None,
                    sandbox_id
                ))
                
                record_id = cursor.fetchone()['id']
                print(f"📊 Logged execution: {record_id}")
                return record_id
                
        except Exception as e:
            print(f"❌ Failed to log execution: {e}")
            return None
    
    def get_recent_health(self, agent_name: str, hours: int = 24) -> Dict[str, Any]:
        """Get recent health stats for an agent"""
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_runs,
                        COUNT(*) FILTER (WHERE success = true) as successful_runs,
                        COUNT(*) FILTER (WHERE success = false) as failed_runs,
                        AVG(duration_ms) as avg_duration_ms,
                        ROUND(
                            (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)), 2
                        ) as success_rate_percent
                    FROM exec_audit_log 
                    WHERE agent_name = %s 
                    AND ts >= NOW() - INTERVAL '%s hours'
                """, (agent_name, hours))
                
                return dict(cursor.fetchone())
        except Exception as e:
            print(f"❌ Failed to get health stats: {e}")
            return {}

class LoggedCodeAgent:
    """CodeAgent wrapper that automatically logs all executions"""
    
    def __init__(self, model, logger: ExecutionLogger, agent_name: str = "default_agent"):
        self.agent = CodeAgent(
            model=model,
            tools=[],
            executor_type="e2b"
        )
        self.logger = logger
        self.agent_name = agent_name
    
    def run(self, task_prompt: str) -> str:
        """Run agent task with automatic logging"""
        start_time = time.time()
        
        try:
            # Execute the task
            result = self.agent.run(task_prompt)
            
            # Calculate duration
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log successful execution
            self.logger.log_execution(
                agent_name=self.agent_name,
                task_prompt=task_prompt,
                success=True,
                duration_ms=duration_ms,
                resource_usage={"result_length": len(str(result))}
            )
            
            return result
            
        except Exception as e:
            # Calculate duration even for failures
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log failed execution
            self.logger.log_execution(
                agent_name=self.agent_name,
                task_prompt=task_prompt,
                success=False,
                duration_ms=duration_ms,
                error_message=str(e)
            )
            
            # Re-raise the exception
            raise e
    
    def get_health(self) -> Dict[str, Any]:
        """Get health statistics for this agent"""
        return self.logger.get_recent_health(self.agent_name)

# Test function
def test_logged_agent():
    """Test the logged agent with database integration"""
    
    # Initialize logger
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        print("❌ Please set DATABASE_URL environment variable")
        return
    
    logger = ExecutionLogger(db_url)
    
    # Initialize OpenAI model
    model = OpenAIModel(
        model_name="gpt-4o-mini",
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # Create logged agent
    agent = LoggedCodeAgent(
        model=model,
        logger=logger,
        agent_name="test_trading_agent"
    )
    
    # Run test tasks
    print("🚀 Running test tasks...")
    
    # Test 1: Simple calculation
    try:
        result1 = agent.run("Calculate the compound interest on $1000 at 5% for 3 years")
        print(f"✅ Test 1 passed: {result1}")
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
    
    # Test 2: Data analysis
    try:
        result2 = agent.run("Generate a simple moving average for the list [100, 102, 98, 105, 110, 108]")
        print(f"✅ Test 2 passed: {result2}")
    except Exception as e:
        print(f"❌ Test 2 failed: {e}")
    
    # Test 3: Intentional failure
    try:
        result3 = agent.run("Import a non-existent library called 'impossible_lib'")
        print(f"Test 3 result: {result3}")
    except Exception as e:
        print(f"✅ Test 3 correctly failed: {e}")
    
    # Show health stats
    health = agent.get_health()
    print(f"\n📊 Agent Health Stats:")
    print(f"   Total runs: {health.get('total_runs', 0)}")
    print(f"   Success rate: {health.get('success_rate_percent', 0)}%")
    print(f"   Avg duration: {health.get('avg_duration_ms', 0)}ms")

if __name__ == "__main__":
    test_logged_agent()
