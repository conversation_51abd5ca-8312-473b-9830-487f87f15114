-- Active: 1736716607014@@127.0.0.1@5432@coveredcalls_agents@public
-- setup_execution_log.sql
-- Run this in your PostgreSQL database

-- Create execution audit log table
CREATE TABLE IF NOT EXISTS exec_audit_log (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ts              TIMESTAMPTZ DEFAULT NOW(),
  agent_name      TEXT NOT NULL,
  task_prompt     TEXT,
  execution_type  TEXT DEFAULT 'e2b',
  success         BOOLEAN,
  duration_ms     INTEGER,
  error_message   TEXT,
  resource_usage  JSONB,  -- Store CPU, memory stats from E2B
  sandbox_id      TEXT,   -- E2B sandbox identifier
  created_at      TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for querying by timestamp and success
CREATE INDEX IF NOT EXISTS idx_exec_audit_ts ON exec_audit_log(ts);
CREATE INDEX IF NOT EXISTS idx_exec_audit_success ON exec_audit_log(success);
CREATE INDEX IF NOT EXISTS idx_exec_audit_agent ON exec_audit_log(agent_name);

-- Create a view for quick health monitoring
CREATE OR REPLACE VIEW agent_health_summary AS
SELECT 
    agent_name,
    DATE_TRUNC('hour', ts) as hour,
    COUNT(*) as total_runs,
    COUNT(*) FILTER (WHERE success = true) as successful_runs,
    COUNT(*) FILTER (WHERE success = false) as failed_runs,
    AVG(duration_ms) as avg_duration_ms,
    ROUND(
        (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)), 2
    ) as success_rate_percent
FROM exec_audit_log 
WHERE ts >= NOW() - INTERVAL '24 hours'
GROUP BY agent_name, DATE_TRUNC('hour', ts)
ORDER BY hour DESC, agent_name;

-- Insert a test record
INSERT INTO exec_audit_log (agent_name, task_prompt, success, duration_ms) 
VALUES ('test_agent', 'print("hello world")', true, 1500);

-- Query to check it works
SELECT * FROM agent_health_summary LIMIT 5;