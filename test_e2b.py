from wheel_trader.exec_manager_e2b import run_job

if __name__ == "__main__":
    # Path to the sample agent
    code_path = "agents/sample_agent.py"
    job_name = "sample_agent_fetch_options"

    # Optionally, set environment variables (e.g., API keys)
    env = {}

    result = run_job(job_name, code_path, env=env)

    print("=== E2B Sandbox Execution Result ===")
    print(f"Job Name: {result['job_name']}")
    print(f"Container ID: {result['container_id']}")
    print(f"Success: {result['success']}")
    print(f"Exit Code: {result['exit_code']}")
    print(f"CPU ms: {result['cpu_ms']}")
    print(f"Duration ms: {result['duration_ms']}")
    print(f"Output:\n{result['output']}")
    print(f"Error:\n{result['error']}")
