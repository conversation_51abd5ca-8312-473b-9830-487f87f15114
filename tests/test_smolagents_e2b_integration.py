#!/usr/bin/env python3
"""
Test suite for smolagents + E2B integration

Tests the secure execution of smolagents tools through E2B sandboxes
with health monitoring and performance tracking.
"""

import os
import sys
import time
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.smolagents_e2b_adapter import (
    SecureE2BTool, 
    SecureOptionsDataTool, 
    SecureMarketAnalysisTool,
    SecurePythonInterpreterTool,
    SecureCodeAgent
)
from wheel_trader.secure_coordinator import SecureCoordinator
from wheel_trader.agent_health import AgentHealthManager


def test_secure_options_data_tool():
    """Test the secure options data tool"""
    print("\n=== Testing Secure Options Data Tool ===")
    
    tool = SecureOptionsDataTool()
    
    try:
        # Test with mock data (will fail due to missing API key, but should execute securely)
        result = tool.forward(symbol="AAPL", expiry_date="2024-12-20")
        
        print(f"Tool execution completed")
        print(f"Result type: {type(result)}")
        print(f"Result: {result}")
        
        # Should return a dict with error about missing API key
        assert isinstance(result, dict), "Result should be a dictionary"
        assert "status" in result, "Result should have status field"
        
        print("✅ Secure Options Data Tool test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_secure_market_analysis_tool():
    """Test the secure market analysis tool"""
    print("\n=== Testing Secure Market Analysis Tool ===")
    
    tool = SecureMarketAnalysisTool()
    
    try:
        # Test RSI analysis
        result = tool.forward(symbol="AAPL", analysis_type="rsi")
        
        print(f"RSI Analysis Result: {result}")
        
        assert isinstance(result, dict), "Result should be a dictionary"
        assert result.get("analysis_type") == "rsi", "Should return RSI analysis"
        assert "rsi_value" in result, "Should contain RSI value"
        
        # Test SMA analysis
        result = tool.forward(symbol="TSLA", analysis_type="sma")
        
        print(f"SMA Analysis Result: {result}")
        
        assert isinstance(result, dict), "Result should be a dictionary"
        assert result.get("analysis_type") == "sma", "Should return SMA analysis"
        assert "sma_20" in result, "Should contain SMA values"
        
        print("✅ Secure Market Analysis Tool test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_secure_python_interpreter_tool():
    """Test the secure Python interpreter tool"""
    print("\n=== Testing Secure Python Interpreter Tool ===")
    
    tool = SecurePythonInterpreterTool()
    
    try:
        # Test simple calculation
        code = """
import math
result = math.sqrt(25)
print(f"Square root of 25 is: {result}")
"""
        
        result = tool.forward(code=code)
        
        print(f"Python execution result: {result}")
        
        assert "Square root of 25 is: 5" in result, "Should execute Python code correctly"
        
        print("✅ Secure Python Interpreter Tool test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_agent_health_manager():
    """Test the agent health management system"""
    print("\n=== Testing Agent Health Manager ===")
    
    health_manager = AgentHealthManager(health_threshold=0.7)
    
    try:
        # Test health check for new agent (should be healthy by default)
        is_healthy = health_manager.is_agent_healthy("test_agent")
        print(f"New agent health status: {is_healthy}")
        
        # Should be healthy for new agents
        assert is_healthy, "New agents should be considered healthy"
        
        # Test getting health metrics (should return None for new agent)
        metrics = health_manager.get_health_metrics("test_agent")
        print(f"Health metrics for new agent: {metrics}")
        
        # Test getting all agent health
        all_health = health_manager.get_all_agent_health()
        print(f"All agent health: {all_health}")
        
        print("✅ Agent Health Manager test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_secure_coordinator():
    """Test the secure coordinator"""
    print("\n=== Testing Secure Coordinator ===")
    
    coordinator = SecureCoordinator()
    
    try:
        # Test tool execution
        result = coordinator.execute_tool_directly(
            "market_analysis", 
            symbol="AAPL", 
            analysis_type="rsi"
        )
        
        print(f"Direct tool execution result: {result}")
        
        assert result["success"], f"Tool execution should succeed: {result.get('error')}"
        assert "result" in result, "Should have result field"
        
        # Test agent status
        status = coordinator.get_agent_status()
        print(f"Coordinator status: {status}")
        
        assert "secure_tools" in status, "Should list secure tools"
        assert "market_analysis" in status["secure_tools"], "Should include market analysis tool"
        
        print("✅ Secure Coordinator test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_secure_code_agent():
    """Test the secure code agent (requires OpenAI API key)"""
    print("\n=== Testing Secure Code Agent ===")
    
    # Skip if no OpenAI API key
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Skipping SecureCodeAgent test - OPENAI_API_KEY not set")
        return
    
    try:
        # Create a secure agent
        agent = SecureCodeAgent(model="gpt-3.5-turbo")
        
        # Test simple task
        task = "Calculate the square root of 144"
        
        print(f"Asking agent: {task}")
        
        # This would normally call the LLM, but we'll just test the setup
        print("Agent created successfully with secure tools")
        
        # Check that secure tools are available
        tool_names = [tool.name for tool in agent.tools]
        print(f"Available tools: {tool_names}")
        
        assert "python_interpreter" in tool_names, "Should have secure Python interpreter"
        
        print("✅ Secure Code Agent setup test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        # Don't raise for this test since it requires external API
        print("Note: This test requires OpenAI API key and may fail due to external dependencies")


def test_error_handling():
    """Test error handling in secure tools"""
    print("\n=== Testing Error Handling ===")
    
    tool = SecurePythonInterpreterTool()
    
    try:
        # Test code that will fail
        error_code = """
print("This will work")
raise ValueError("Intentional error for testing")
print("This should not print")
"""
        
        result = tool.forward(code=error_code)
        
        print(f"Error handling result: {result}")
        
        # Should contain the error information
        assert "ValueError" in result or "Intentional error" in result, "Should capture the error"
        
        print("✅ Error handling test passed!")
        
    except RuntimeError as e:
        # This is expected if the tool execution fails
        print(f"Expected error caught: {e}")
        print("✅ Error handling test passed!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        raise


def run_all_tests():
    """Run all tests"""
    print("🚀 Starting smolagents + E2B Integration Tests...")
    
    tests = [
        test_secure_options_data_tool,
        test_secure_market_analysis_tool,
        test_secure_python_interpreter_tool,
        test_agent_health_manager,
        test_secure_coordinator,
        test_secure_code_agent,
        test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        sys.exit(1)


if __name__ == "__main__":
    run_all_tests()
